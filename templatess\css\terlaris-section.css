.terlaris {
  max-width: 1200px;
  margin: 40px auto;
  padding: 30px;
  background-color: var(--secondary-color);
  font-family: var(--main-font);
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto auto;
  gap: 20px;
  grid-template-areas:
    "header header"
    "olt ont"
    "products products";
}

.terlaris h1:first-child {
  display: inline-block;
  font-size: 24px;
  font-weight: var(--font-weight-bold);
  color: var(--grey-text);
  margin: 10px 0 10px 0;
  grid-area: header;
  justify-self: start;
}

.terlaris hr {
  grid-area: header;
  width: 200px;
  border: none;
  border-top: 2px solid #D9D9D9;
  align-self: end;
  justify-self: start;
}

.terlaris > button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 5px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  grid-area: header;
  justify-self: end;
  align-self: center;
}

.terlaris > button:hover {
    background-color: #285eff;
}

.terlaris-olt,
.terlaris-ont {
  display: flex;
  background-color: var(--secondary-color);
  align-items: center;
  gap: 20px;
  min-height: 181px;
  border: #D9D9D9 solid 1px;
}

.terlaris-olt {
  grid-area: olt;
}

.terlaris-ont {
  grid-area: ont;
}

.terlaris-olt button,
.terlaris-ont button {
  display: flex;
  width: 100%;
  height: 100%;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  text-align: left;
  align-items: center;
  gap: 20px;
}

.terlaris-olt button img,
.terlaris-ont button img {
  width: 181px;
  height: 181px;
  flex-shrink: 0;
}

.terlaris-content {
  flex: 1;
  padding: 5px 20px 10px 5px;
}

.terlaris-content h1 {
  font-size: var(--font-size-medium);
  color: var(--grey-text);
  margin: 0 0 20px 0;
  font-weight: var(--font-weight-regular);
}

.terlaris-content p {
  font-size: 16px;
  color: var(--grey-text);
  margin: 0 0 30px 0;
  font-weight: var(--font-weight-regular);
}

.price {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
  gap: 10px;
}

.price p {
  color: var(--primary-color);
  font-size: 16px;
  font-weight: bold;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.price .price-cart {
  width: 19.69px;
  height: 17px;
}

.price a {
  color: var(--grey-text);
  text-decoration: none;
  font-size: 14px;
}

.price a:hover {
  text-decoration: underline;
}

.terlaris-products {
  grid-area: products;
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 20px;
}

.terlaris-products > div {
  background-color: var(--secondary-color);
  text-align: left;
  border: #D9D9D9 solid 1px;
  width: 170px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
}

.terlaris-products a {
  text-decoration: none;
  color: inherit;
  display: block;
  width: 100%;
}

.terlaris-products img {
  width: 100%;
  object-fit: contain;
  margin: 0;
}

.terlaris-products h1 {
  font-size: var(--font-size-medium);
  color: var(--grey-text);
  margin: 5px 0 8px 0;
  padding: 0 10px 0 10px;
  font-weight: var(--font-weight-semibold);
  width: 100%;
  text-align: left;
}

.terlaris-products p {
  color: var(--primary-color);
  font-size: var(--font-size-medium);
  font-weight: var(--font-weight-semibold);
  margin: 0;
  padding: 0 10px 10px 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.terlaris-products .price-cart {
  width: 19.69px;
  height: 17px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .terlaris {
    grid-template-columns: 1fr;
    grid-template-areas:
      "header"
      "olt"
      "ont"
      "products";
  }

  .terlaris-products {
    grid-template-columns: repeat(2, 1fr);
  }

  .terlaris-olt,
  .terlaris-ont {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .terlaris-products {
    grid-template-columns: 1fr;
  }
}