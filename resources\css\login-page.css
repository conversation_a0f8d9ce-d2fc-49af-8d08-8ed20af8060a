body {
    text-decoration: none;
    list-style: none;
    min-height: 100vh;
    background: linear-gradient(90deg, rgba(221,220,255,1) 0%, rgba(144,200,255,1) 45%, rgba(144,200,255,1) 100%);
}

main {
    display: flex;
    width: 100%;
    height: 100vh;
    align-content: center;
    justify-content: center;
}

.cards {
    display: flex;
    align-items: center;
}

.card-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-areas: 
        "image form";
    align-items: center;
    justify-content: center;
    background-color: white;
    padding: 2rem;
    border-radius: 1rem;
    gap: 2rem;
}

.image-content {
    grid-area: image;
    background-image: url('../images/pexels-brett-sayles-1624895.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    align-items: start;
    justify-content: end;
    gap: 10px;
    height: 500px;
    border-radius: 10px;
}

.image-content button {
    margin: 1em 1em 1em 0;
    padding: 0.2em;
    background-color: rgba(255, 255, 255, 0);
    width: 5em;
    border-radius: 20px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid white;
    color: white;
}

.image-content button:hover {
    background-color: rgba(255, 255, 255, 0.263);
    border: 1p solid black;
}

.form-content {
    grid-area: form;
}

.heading-logo {
    margin-top: 0;
    margin-bottom: 80px;
    align-content: center;
    justify-content: start;
}

.logo {
    width: 100px;
}

.navigation {
    padding: 5px 15px 1px;
    display: flex;
    width: 100%;
    justify-content: end;
}

.navigation > button {
    color: rgb(35, 145, 255);
}

.navigation hr {
    height: 100%;
    width: 1px; 
    background-color: black;
    margin: 0 10px;
}