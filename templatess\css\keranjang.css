@import url('root.css');

.keranjang-grid {
    display: flex;
    justify-content: space-between;
    gap: 30px;
    padding: 0 30px;
    margin-top: 20px;
}

.keranjang-section {
    flex: 1;
    width: 100%;
}

.keranjang-section {
    max-width: 1200px;
    margin: 40px auto;
    padding: 30px;
    background-color: var(--secondary-color);
    font-family: var(--main-font);
    display: flex;
    gap: 30px;
}

.keranjang-content {
    flex: 1;
}

.keranjang-ont {
    width: 100%;
    margin-bottom: 30px;
}

.keranjang-ont-header {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
}

.keranjang-ont-header .header-top {
    display: flex;
    align-items: center;
    gap: 15px;
}

.keranjang-ont-header input[type="checkbox"] {
    appearance: none;
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid var(--primary-color);
    border-radius: 4px;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
}

.keranjang-ont-header input[type="checkbox"]:checked {
    background-color: var(--primary-color);
}

.keranjang-ont-header input[type="checkbox"]:checked::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.keranjang-ont-header h1 {
    font-size: var(--font-size-xlarge);
    font-weight: var(--font-weight-semibold);
    color: var(--grey-text);
    margin: 0;
}

.keranjang-ont-header hr {
    width: 100%;
    border: none;
    border-top: 2px solid #D9D9D9;
    margin: 0;
}

.keranjang-product-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.keranjang-product {
    display: flex;
    gap: 20px;
    border: 1px solid #D9D9D9;
    border-radius: 8px;
}

.keranjang-product img {
    width: 167px;
    height: 167px;
    object-fit: contain;
}

.keranjang-product-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    gap: 15px;
}

.keranjang-product-details h1 {
    font-size: var(--font-size-medium);
    font-weight: var(--font-weight-semibold);
    color: var(--grey-text);
    margin: 0;
}

.keranjang-product-details p {
    font-size: var(--font-size-small);
    color: var(--primary-color);
    font-weight: var(--font-weight-semibold);
    margin: 0;
}

.product-size-order {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 10px;
}

.product-size-order p {
    color: var(--grey-text);
    font-size: var(--font-size-small);
    margin: 0;
}

.size-add {
    display: flex;
    align-items: center;
}

.size-add button {
    width: 30px;
    height: 30px;
    border: 1px solid #D9D9D9;
    background-color: var(--grey-text);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-small);
    color: var(--light-text);
    transition: all 0.3s ease;
}

.size-add button:hover {
    background-color: var(--primary-color);
    color: var(--light-text);
    border-color: var(--primary-color);
}

.size-add input[type="number"] {
    width: 30px;
    height: 30px;
    border: 1px solid #D9D9D9;
    text-align: center;
    font-size: var(--font-size-small);
    color: var(--grey-text);
    --moz-appearance: textfield;
}

.size-add input[type="number"]::-webkit-outer-spin-button,
.size-add input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.keranjang-product-details h2 {
    font-size: var(--font-size-medium);
    font-weight: var(--font-weight-semibold);
    color: var(--grey-text);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.keranjang-product-details h2 span {
    color: var(--primary-color);
}

.keranjang-product-details .price-action {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 20px;
}

.whatsapp-button {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-color);
    color: var(--light-text);
    border: none;
    padding: 2px 10px;
    border-radius: 10px;
    cursor: pointer;
    font-size: var(--font-size-small);
    font-weight: var(--font-weight-semibold);
    transition: background-color 0.3s ease;
}

.whatsapp-button:hover {
    background-color: #285eff;
}

.whatsapp-button img {
    width: 20px;
    height: 20px;
}

.whatsapp-button p {
    color: var(--light-text);
    margin: 0;
    font-size: var(--font-size-small);
    font-weight: var(--font-weight-semibold);
}

.order-summary {
    width: 300px;
    background-color: var(--secondary-color);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin: 40px 0 0 0;
    height: fit-content;
}

.summary-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.summary-content h1 {
    font-size: var(--font-size-large);
    font-weight: var(--font-weight-semibold);
    color: var(--grey-text);
    margin: 0;
}

.summary-content hr {
    width: 100%;
    border: none;
    border-top: 2px solid #D9D9D9;
    margin: 0;
}

.summary-details {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.summary-item p {
    margin: 0;
    font-size: var(--font-size-medium);
    color: var(--grey-text);
}

.summary-item .total-items,
.summary-item .total-price {
    font-weight: var(--font-weight-semibold);
    color: var(--primary-color);
}

.pay-button {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-color);
    color: var(--light-text);
    border: none;
    padding: 10px 20px;
    border-radius: 10px;
    cursor: pointer;
    font-size: var(--font-size-medium);
    font-weight: var(--font-weight-semibold);
    transition: background-color 0.3s ease;
    width: 100%;
}

.pay-button:hover {
    background-color: #285eff;
}

.pay-button p {
    margin: 0;
    color: var(--light-text);
}

/* Mobile Responsive */
@media screen and (max-width: 768px) {
    .keranjang-grid {
        flex-direction: column;
        padding: 0 15px;
        gap: 20px;
    }

    .order-summary {
        width: 100%;
    }

    .keranjang-product {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .keranjang-product img {
        width: 200px;
        height: 200px;
    }

    .keranjang-product-details {
        align-items: center;
    }

    .product-size-order {
        flex-direction: column;
    }
} 