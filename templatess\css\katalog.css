@import url('root.css');

.katalog-section {
    flex: 1;
    padding: 40px;
    display: flex;
    flex-direction: column;
    background-color: var(--secondary-color);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    width: 800px;
}

.katalog-content {
    flex: 1;
}

.katalog-header {
    margin-bottom: 10px;
}

.katalog-header h1 {
    font-family: var(--main-font);
    font-size: var(--font-size-xlarge);
    font-weight: var(--font-weight-bold);
    color: var(--grey-text);
    margin-bottom: 15px;
}

.katalog-header hr {
    border: none;
    border-top: 3px solid #D9D9D9;
    width: 250px;
    margin-bottom: 30px;
}

.filter-header {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
}

.filter-header h2 {
    font-family: var(--main-font);
    font-size: var(--font-size-medium);
    font-weight: var(--font-weight-regular);
    margin: 0 20px 0 0;
    padding: 0 250px 0 0;
}

.filter-header img {
    height: 30px;
    width: 30px;
    margin: 0 0 0 10px;
}

.filter-price-dropdown {
    padding: 5px 15px;
    width: 250px;
    border: 2px solid #D9D9D9;
    background-color: var(--secondary-color);
    border-radius: 15px;
    position: absolute;
    z-index: 10;
    top: 395px;
}

.dropdown-button {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.dropdown-button span {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.dropdown-button span p {
    margin: 0;
}

.dropdown-button span img {
    margin-left: auto;
}

.price-filter-options {
    padding: 0 0 10px 0;
}

.price-filter-options .price-range {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-small);
}

.price-filter-options hr{
    width: 200px;
    margin-bottom: 15px;
}

.katalog-products {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
  margin: 10px 0 0 0;
}

.products {
  background-color: var(--secondary-color);
  text-align: left;
  border: #d9d9d9 solid 1px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  position: relative;
  padding: 0;
}

.products a {
  text-decoration: none;
  color: inherit;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.products img {
  width: 100%;
  height: 160px;
  object-fit: cover;
  margin: 0;
  display: block;
}

.products h1 {
  font-size: 14px;
  color: var(--grey-text);
  margin: 10px 0 15px 0;
  padding: 0 8px 0 8px;
  font-weight: var(--font-weight-semibold);
  width: 100%;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 4px;
  line-height: 1.2;
}

.products p {
  color: var(--primary-color);
  font-size: 13px;
  font-weight: var(--font-weight-semibold);
  margin: 0;
  padding: 0 8px 10px 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: auto;
  line-height: 1.2;
}

.products .diskon-price {
  color: #fa766a;
  font-size: 14px;
  text-decoration: line-through;
  margin: 0;
  padding: 0 10px;
}

.products .price-cart {
  width: 30px;
  height: 25px;
}

.checkbox-item input[type="radio"] {
    appearance: none;
    --webkit-appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid #D9D9D9;
    border-radius: 4px;
    margin-right: 10px;
    position: relative;
    cursor: pointer;
    vertical-align: middle;
}

.checkbox-item input[type="radio"]:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-item input[type="radio"]:checked::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 10px;
    height: 10px;
    background-color: white;
    border-radius: 2px;
}

.checkbox-item label {
    cursor: pointer;
    display: flex;
    align-items: center;
    font-family: var(--main-font);
    font-size: var(--font-size-small);
    color: var(--grey-text);
}

@media screen and (max-width: 992px) {
    .katalog-section {
        flex-direction: column;
        gap: 20px;
    }

    .katalog-products {
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
    }
}

@media screen and (max-width: 768px) {
    .katalog-section {
        padding: 30px 0;
    }

    .katalog-header {
        margin-bottom: 20px;
    }

    .katalog-header h1 {
        font-size: var(--font-size-large);
    }

    .katalog-products {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
}

@media screen and (max-width: 480px) {
    .katalog-products {
        grid-template-columns: 1fr;
    }
} 