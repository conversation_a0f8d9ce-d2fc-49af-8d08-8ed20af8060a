.break-contact {
    max-width: 600px;
    margin: 40px auto;
    padding: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.break-contact h1 {
    color: var(--grey-text);
    font-family: var(--main-font);
    font-size: var(--font-size-xxlarge);
    font-weight: var(--font-weight-semibold);
    text-align: center;
    margin: 0;
}

.break-contact h1 span {
    color: var(--primary-color);
}

.break-contact button {
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: var(--font-size-medium);
    font-weight: var(--font-weight-semibold);
    transition: background-color 0.3s ease;
}

.break-contact button:hover {
    background-color: #285eff;
}

.break-contact img {
    width: 24px;
    height: 24px;
    object-fit: contain;
}