.login-main {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #efefef;
    padding: 20px;
  }
  
  .login-bg-center {
    background: url("../assets/images/Login-bg.png") center center / cover
      no-repeat;
    border-radius: 18px;
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.1);
    padding: 40px 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 350px;
    min-height: 340px;
    max-width: 800px;
    width: 100%;
  }
  
  .login-container {
    background: #fff;
    border-radius: 18px;
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.1);
    padding: 40px 32px 32px 32px;
    min-width: 0;
    max-width: 450px;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-left: auto;
  }
  
  .login-title {
    font-size: 2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 32px;
    border-bottom: 3px solid #0b46f9;
    display: inline-block;
    padding-bottom: 6px;
    text-align: center;
  }
  
  .login-form {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 12px;
    box-sizing: border-box;
  }
  
  .login-form label {
    font-size: 1rem;
    color: #222;
    margin-bottom: 2px;
  }
  
  .login-form input {
    width: 100%;
    padding: 12px 14px;
    border: 1.5px solid #d1d5db;
    border-radius: 8px;
    font-size: 1rem;
    outline: none;
    box-sizing: border-box;
    transition: border-color 0.2s, box-shadow 0.2s;
  }
  
  .login-form input[type="email"]:focus,
  .login-form input[type="password"]:focus {
    border-color: #0b46f9;
    box-shadow: 0 0 0 2px rgba(11, 70, 249, 0.2);
  }
  
  .password-input {
    position: relative;
    display: flex;
    align-items: center;
  }
  
  .password-input input[type="password"] {
    padding-right: 40px; /* ruang untuk ikon mata */
  }
  
  .toggle-password {
    position: absolute;
    right: 14px;
    top: 50%;
    transform: translateY(-50%);
    width: 22px;
    height: 22px;
    cursor: pointer;
    opacity: 0.75;
    transition: opacity 0.2s;
  }
  
  .login-button {
    margin-top: 10px;
    padding: 10px 0;
    background: #0b46f9;
    color: #fff;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.2s;
  }
  
  .login-button:hover {
    background: #0072ff;
  }
  
  .register-link {
    margin-top: 16px;
    font-size: 0.98rem;
    color: #444;
  }
  
  .register-link a {
    color: #1e90ff;
    text-decoration: underline;
  }
  
@media (max-width: 700px) {
    .login-bg-center {
        padding: 16px 2vw;
        min-width: 0;
        max-width: 98vw;
    }
    .login-container {
        min-width: 0;
        max-width: 98vw;
        padding: 24px 8px;
    }
}