@import url("root.css");

body {
  font-family: var(--main-font);
  color: var(--grey-text);
  background-color: var(--secondary-color);
  margin: 0;
  padding: 0;
}

.contact-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.contact-main {
  flex: 1;
  padding: 2rem;
}

.contact-content {
  max-width: 1200px;
  margin: 0 auto;
}

.contact-title-wrapper {
  text-align: center;
  margin-bottom: 1rem;
}

.contact-title {
  font-size: var(--font-size-xxlarge);
  font-weight: var(--font-weight-bold);
  color: #444;
}

.highlight {
  color: #444;
}

.contact-info-container {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
  margin-top: 3rem;
}

.contact-map {
  flex: 1 1 320px;
  max-width: 400px;
}

.contact-map img {
  width: 100%;
  border-radius: 12px;
  border: 1px solid #eee;
  background: #f7f7f7;
}

.contact-details {
  flex: 2 1 320px;
  min-width: 260px;
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.contact-icon {
  background: var(--primary-color);
  color: var(--light-text);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-icon img {
  width: 22px;
  height: 22px;
}

.contact-cta {
  text-align: center;
  margin-top: 2rem;
  font-size: var(--font-size-large);
}

.contact-cta p {
  margin-bottom: 1.2rem;
}

.admin-link {
  color: var(--primary-color);
  cursor: pointer;
}

.contact-buttons {
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
}

.contact-buttons button {
  padding: 7px 16px;
  font-size: 0.95rem;
  border-radius: 6px;
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  border: none;
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: var(--main-font);
}

.contact-buttons .btn-outline {
  border: 2px solid var(--primary-color);
  background-color: transparent;
  color: var(--primary-color);
}

.contact-buttons button:not(.btn-outline) {
  background-color: var(--primary-color);
  color: var(--light-text);
}

.contact-buttons img {
  width: 18px;
  height: 18px;
}

hr {
  border: none;
  border-top: 1px solid #9c9c9c;
  margin: 0.75rem 0;
}

.footer {
  text-align: center;
  padding: 1.5rem;
  background-color: var(--primary-color);
  font-size: var(--font-size-medium);
  color: var(--light-text);
}
