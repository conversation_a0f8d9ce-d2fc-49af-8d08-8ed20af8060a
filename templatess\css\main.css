@import url("https://cdnjs.cloudflare.com/ajax/libs/meyer-reset/2.0/reset.min.css");
@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Istok+Web:ital,wght@0,400;0,700;1,400;1,700&family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap');

@import url('root.css');
@import url('navigation.css');
@import url('header.css');
@import url('terlaris-section.css');
@import url('diskon-section.css');
@import url('terbaru-section.css');
@import url('break-contact.css');
@import url('sponsor-section.css');
@import url('footer.css');

@import url('benefit-section.css');
@import url('aside.css');
@import url('katalog.css');

@import url('detail-produk.css');
@import url('keranjang.css');
@import url('history.css');

@import url('login.css');
@import url('register.css');
@import url('about.css');
@import url('register.css');

@import url('responsive.css');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: var(--main-font);
}

a {
    text-decoration: none;
}

body {
    background-color: #EFEFEF;
}

.main-catalog {
    margin: 80px 60px 20px 60px;
    display: flex;
    gap: 40px;
}

.price-filter-options {
    display: none;
}