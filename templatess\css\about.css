@import url("root.css");

.about-main {
  padding: 40px 20px;
  background-color: var(--secondary-color);
  font-family: var(--main-font);
  min-height: 100px;
}

.about-content {
  max-width: 800px;
  margin: 0 auto;
  background-color: var(--secondary-color);
  padding: 40px;
  color: var(--grey-text);
}

.about-title-wrapper {
  text-align: center;
  margin-bottom: 32px;
}

.about-title {
  font-size: var(--font-size-xxlarge);
  font-weight: var(--font-weight-semibold);
  color: var(--dark-text);
  display: inline-block;
  font-family: var(--main-font);
  position: relative;
}

.highlight {
  position: relative;
  display: inline-block;
}

.highlight::after {
  content: "";
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

.about-section h3 {
  font-size: var(--font-size-large);
  font-weight: var(--font-weight-semibold);
  margin: 36px 0 24px;
  color: var(--grey-text);
  border-left: 4px solid var(--primary-color);
  padding-left: 10px;
}

.about-section p,
.about-section ol {
  font-size: var(--font-size-medium);
  line-height: 1.6;
  color: var(--grey-text);
  margin-bottom: 16px;
  font-weight: var(--font-weight-regular);
}

.about-section ol {
  list-style: decimal !important;
  list-style-position: outside;
  padding-left: 20px;
  margin-left: 1em;
}

.about-section .section-row {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 32px;
}

.section-title {
  min-width: 120px;
  font-size: var(--font-size-large);
  font-weight: var(--font-weight-semibold);
  color: var(--grey-text);
  border-left: 4px solid var(--primary-color);
  padding-left: 10px;
  margin: 0;
}

.section-content {
  flex: 1;
  color: var(--grey-text);
  font-size: var(--font-size-medium);
  line-height: 1.6;
  font-weight: var(--font-weight-regular);
}

.section-content ol {
  list-style: decimal;
  padding-left: 20px;
  margin: 0;
}

.section-content p {
  margin-bottom: 12px;
}

.about-cta {
  text-align: center;
  margin-top: 40px;
}

.about-cta p {
  font-size: var(--font-size-large);
  font-weight: var(--font-weight-medium);
  color: var(--dark-text);
  margin-bottom: 16px;
}

.admin-link {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
}

.about-buttons {
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
}

.about-buttons button {
  padding: 7px 16px;
  font-size: 0.95rem;
  border-radius: 6px;
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  border: none;
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: var(--main-font);
}

.about-buttons .btn-outline {
  border: 2px solid var(--primary-color);
  background-color: transparent;
  color: var(--primary-color);
}

.about-buttons button:not(.btn-outline) {
  background-color: var(--primary-color);
  color: var(--light-text);
}

.about-buttons img {
  width: 18px;
  height: 18px;
}

.about-section hr {
  border: none;
  border-top: 2px solid var(--primary-color);
  width: 100%;
  margin: 32px 0;
}

.about-section p strong {
  font-weight: var(--font-weight-bold);
}

.visi-misi-paragraph {
  margin-left: 16px;
}

.line-separator hr {
  margin: 4rem 0;
  border: none;
  border-top: 1px solid #9c9c9c;
  width: 100%;
}
