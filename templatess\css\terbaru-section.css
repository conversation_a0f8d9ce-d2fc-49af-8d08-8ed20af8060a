.terbaru {
  max-width: 1200px;
  margin: 40px auto;
  padding: 30px;
  background-color: var(--secondary-color);
  font-family: var(--main-font);
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto auto;
  gap: 20px;
  grid-template-areas:
    "header header"
    "olt ont"
    "products products";
}

.terbaru h1:first-child {
  display: inline-block;
  font-size: 24px;
  font-weight: var(--font-weight-bold);
  color: var(--grey-text);
  margin: 10px 0 10px 0;
  grid-area: header;
  justify-self: start;
}

.terbaru hr {
  grid-area: header;
  width: 200px;
  border: none;
  border-top: 2px solid #D9D9D9;
  align-self: end;
  justify-self: start;
}

.terbaru > button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 5px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  grid-area: header;
  justify-self: end;
  align-self: center;
}

.terbaru > button:hover {
    background-color: #285eff;
}

.terbaru-olt,
.terbaru-ont {
  display: flex;
  background-color: var(--secondary-color);
  align-items: center;
  gap: 20px;
  min-height: 181px;
  border: #D9D9D9 solid 1px;
}

.terbaru-olt {
  grid-area: olt;
}

.terbaru-ont {
  grid-area: ont;
}

.terbaru-olt button,
.terbaru-ont button {
  display: flex;
  width: 100%;
  height: 100%;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  text-align: left;
  align-items: center;
  gap: 20px;
}

.terbaru-olt button img,
.terbaru-ont button img {
  width: 181px;
  height: 181px;
  flex-shrink: 0;
}

.terbaru-content {
  flex: 1;
  padding: 5px 20px 10px 5px;
}

.terbaru-content h1 {
  font-size: var(--font-size-medium);
  color: var(--grey-text);
  margin: 0 0 20px 0;
  font-weight: var(--font-weight-regular);
}

.terbaru-content p {
  font-size: 16px;
  color: var(--grey-text);
  margin: 0 0 30px 0;
  font-weight: var(--font-weight-regular);
}

.price {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
  gap: 10px;
}

.price p {
  color: var(--primary-color);
  font-size: 16px;
  font-weight: bold;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.price .price-cart {
  width: 19.69px;
  height: 17px;
}

.price a {
  color: var(--grey-text);
  text-decoration: none;
  font-size: 14px;
}

.price a:hover {
  text-decoration: underline;
}

.terbaru-products {
  grid-area: products;
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 20px;
}

.terbaru-products > div {
  background-color: var(--secondary-color);
  text-align: left;
  border: #D9D9D9 solid 1px;
  width: 170px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  position: relative;
}

.terbaru-products a {
  text-decoration: none;
  color: inherit;
  display: block;
  width: 100%;
}

.terbaru-products img {
  width: 100%;
  object-fit: contain;
  margin: 0;
}

.terbaru-products h1 {
  font-size: var(--font-size-medium);
  color: var(--grey-text);
  margin: 5px 0 15px 0;
  padding: 0 10px 0 10px;
  font-weight: var(--font-weight-semibold);
  width: 100%;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 8px;
}

.terbaru-products p {
  color: var(--primary-color);
  font-size: var(--font-size-medium);
  font-weight: var(--font-weight-semibold);
  margin: 0;
  padding: 0 10px 10px 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.terbaru-products .price-cart {
  width: 19.69px;
  height: 17px;
}

.terbaru-tag {
  background-color: var(--primary-color);
  color: var(--light-text);
  padding: 2px 6px;
  font-size: 14px;
  font-weight: var(--font-weight-regular);
  border-radius: 4px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .terbaru {
    grid-template-columns: 1fr;
    grid-template-areas:
      "header"
      "olt"
      "ont"
      "products";
  }

  .terbaru-products {
    grid-template-columns: repeat(2, 1fr);
  }

  .terbaru-olt,
  .terbaru-ont {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .terbaru-products {
    grid-template-columns: 1fr;
  }
} 