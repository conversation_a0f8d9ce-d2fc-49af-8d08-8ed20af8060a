@import 'tailwindcss';
@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Istok+Web:ital,wght@0,400;0,700;1,400;1,700&family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap');
@import url('https://unpkg.com/leaflet@1.9.4/dist/leaflet.css');

@plugin 'tailwindcss-animate';

/* Root CSS Variables from templates/css/root.css */
:root {
    --primary-color: #0B46F9;
    --secondary-color: #FFFFFF;
    --tertiary-color: #7D7D7D;

    --main-font: 'Montserrat', sans-serif;
    --secondary-font: 'Istok Web', sans-serif;

    --light-text: #FFFFFF;
    --dark-text: #000000;
    --grey-text: #53535B;
    --color-white: #FFFFFF;

    --font-size-small: 12px;
    --font-size-medium: 16px;
    --font-size-large: 20px;
    --font-size-xlarge: 24px;
    --font-size-xxlarge: 32px;

    --font-weight-light: 300;
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;
}

@source '../views';
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';

@custom-variant dark (&:is(.dark *));

@theme {
    --font-sans:
        'Montserrat', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    --radius-lg: var(--radius);
    --radius-md: calc(var(--radius) - 2px);
    --radius-sm: calc(var(--radius) - 4px);

    --color-background: var(--background);
    --color-foreground: var(--foreground);

    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);

    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);

    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);

    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);

    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);

    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);

    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);

    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);

    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);

    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

:root {
    /* Font families */
    --font-main: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif;
    --font-secondary: 'Inter', ui-sans-serif, system-ui, sans-serif;

    /* Font sizes */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;

    /* Font weights */
    --font-weight-light: 300;
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;

    /* Colors from template */
    --color-primary: #0B46F9;
    --color-secondary: #FFFFFF;
    --color-tertiary: #7D7D7D;
    --color-grey-text: #53535B;
    --color-dark-text: #000000;
    --color-light-text: #FFFFFF;

    /* Rest of your existing root variables */
    --background: oklch(1 0 0);
    --foreground: oklch(0.145 0 0);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.145 0 0);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.145 0 0);
    --primary: #0B46F9;
    --primary-foreground: #FFFFFF;
    --secondary: oklch(0.97 0 0);
    --secondary-foreground: oklch(0.205 0 0);
    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.556 0 0);
    --accent: oklch(0.97 0 0);
    --accent-foreground: oklch(0.205 0 0);
    --destructive: oklch(0.577 0.245 27.325);
    --destructive-foreground: oklch(0.577 0.245 27.325);
    --border: oklch(0.922 0 0);
    --input: oklch(0.922 0 0);
    --ring: oklch(0.87 0 0);
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
    --radius: 0.625rem;
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.145 0 0);
    --sidebar-primary: oklch(0.205 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.97 0 0);
    --sidebar-accent-foreground: oklch(0.205 0 0);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.87 0 0);
}

.dark {
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.145 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.145 0 0);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.985 0 0);
    --primary-foreground: oklch(0.205 0 0);
    --secondary: oklch(0.269 0 0);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: oklch(0.269 0 0);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: oklch(0.396 0.141 25.723);
    --destructive-foreground: oklch(0.637 0.237 25.331);
    --border: oklch(0.269 0 0);
    --input: oklch(0.269 0 0);
    --ring: oklch(0.439 0 0);
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.985 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(0.269 0 0);
    --sidebar-ring: oklch(0.439 0 0);
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
        font-family: var(--main-font);
        color: var(--dark-text);
    }
}

/* Additional utility classes for text colors */
.text-primary-color {
    color: var(--primary-color) !important;
}

.text-grey-text {
    color: var(--grey-text) !important;
}

.text-light-text {
    color: var(--light-text) !important;
}

.text-dark-text {
    color: var(--dark-text) !important;
}

.bg-primary-color {
    background-color: var(--primary-color) !important;
}

.bg-secondary-color {
    background-color: var(--secondary-color) !important;
}

/* Admin-specific colorful styling */
:root {
    --admin-blue: #3B82F6;
    --admin-green: #10B981;
    --admin-yellow: #F59E0B;
    --admin-purple: #8B5CF6;
    --admin-pink: #EC4899;
    --admin-orange: #F97316;
    --admin-cyan: #06B6D4;
    --admin-indigo: #6366F1;
}

.admin-page {
    color: var(--light-text) !important;
}

.admin-page h1,
.admin-page h2,
.admin-page h3,
.admin-page h4,
.admin-page h5,
.admin-page h6 {
    color: var(--light-text) !important;
}

.admin-page p,
.admin-page span,
.admin-page div,
.admin-page td,
.admin-page th,
.admin-page label {
    color: var(--light-text) !important;
}

/* Override specific Tailwind classes for admin pages */
.admin-page .text-muted-foreground {
    color: rgba(255, 255, 255, 0.7) !important;
}

.admin-page .text-foreground {
    color: var(--light-text) !important;
}

.admin-page .text-sm,
.admin-page .text-xs,
.admin-page .text-base,
.admin-page .text-lg,
.admin-page .text-xl,
.admin-page .text-2xl {
    color: var(--light-text) !important;
}

/* Colorful admin stat cards */
.admin-stat-card-blue {
    background: linear-gradient(135deg, var(--admin-blue), #1E40AF) !important;
    border: 1px solid var(--admin-blue) !important;
}

.admin-stat-card-green {
    background: linear-gradient(135deg, var(--admin-green), #059669) !important;
    border: 1px solid var(--admin-green) !important;
}

.admin-stat-card-yellow {
    background: linear-gradient(135deg, var(--admin-yellow), #D97706) !important;
    border: 1px solid var(--admin-yellow) !important;
}

.admin-stat-card-purple {
    background: linear-gradient(135deg, var(--admin-purple), #7C3AED) !important;
    border: 1px solid var(--admin-purple) !important;
}

.admin-stat-card-pink {
    background: linear-gradient(135deg, var(--admin-pink), #DB2777) !important;
    border: 1px solid var(--admin-pink) !important;
}

.admin-stat-card-orange {
    background: linear-gradient(135deg, var(--admin-orange), #EA580C) !important;
    border: 1px solid var(--admin-orange) !important;
}

/* Colorful text classes for admin */
.admin-text-blue {
    color: var(--admin-blue) !important;
}

.admin-text-green {
    color: var(--admin-green) !important;
}

.admin-text-yellow {
    color: var(--admin-yellow) !important;
}

.admin-text-purple {
    color: var(--admin-purple) !important;
}

.admin-text-pink {
    color: var(--admin-pink) !important;
}

.admin-text-orange {
    color: var(--admin-orange) !important;
}

.admin-text-cyan {
    color: var(--admin-cyan) !important;
}

.admin-text-indigo {
    color: var(--admin-indigo) !important;
}

/* Colorful backgrounds for admin elements */
.admin-bg-blue {
    background-color: var(--admin-blue) !important;
}

.admin-bg-green {
    background-color: var(--admin-green) !important;
}

.admin-bg-yellow {
    background-color: var(--admin-yellow) !important;
}

.admin-bg-purple {
    background-color: var(--admin-purple) !important;
}

/* Keep specific colored text for status indicators */
.admin-page .text-red-500,
.admin-page .text-green-500,
.admin-page .text-blue-500,
.admin-page .text-yellow-500 {
    /* Keep these colors for status indicators */
}

/* Image cropper dialog styling for admin pages */
.admin-page [data-slot="dialog-content"] {
    background-color: var(--sidebar) !important;
    border-color: var(--sidebar-border) !important;
}

.admin-page [data-slot="dialog-title"],
.admin-page [data-slot="dialog-content"] h1,
.admin-page [data-slot="dialog-content"] h2,
.admin-page [data-slot="dialog-content"] h3,
.admin-page [data-slot="dialog-content"] p,
.admin-page [data-slot="dialog-content"] span,
.admin-page [data-slot="dialog-content"] div {
    color: var(--light-text) !important;
}

/* Card styling for admin pages */
.admin-page [data-slot="card"],
.admin-page .bg-card {
    background-color: var(--sidebar) !important;
    border-color: var(--sidebar-border) !important;
    color: var(--light-text) !important;
}

.admin-page [data-slot="card-header"] {
    background-color: transparent !important;
    border-bottom-color: var(--sidebar-border) !important;
}

.admin-page [data-slot="card-content"] {
    background-color: transparent !important;
}

.admin-page [data-slot="card-title"],
.admin-page .text-card-foreground {
    color: var(--light-text) !important;
}

/* Override CSS custom properties for admin pages */
.admin-page {
    --card: var(--sidebar) !important;
    --card-foreground: var(--light-text) !important;
    --muted: var(--sidebar-accent) !important;
    --muted-foreground: rgba(255, 255, 255, 0.7) !important;
    --border: var(--sidebar-border) !important;
    --input: var(--sidebar-accent) !important;
}

/* Input styling for admin pages */
.admin-page input[type="text"],
.admin-page input[type="email"],
.admin-page input[type="password"],
.admin-page textarea,
.admin-page select {
    background-color: var(--sidebar-accent) !important;
    border-color: var(--sidebar-border) !important;
    color: var(--light-text) !important;
}

.admin-page input[type="text"]:focus,
.admin-page input[type="email"]:focus,
.admin-page input[type="password"]:focus,
.admin-page textarea:focus,
.admin-page select:focus {
    border-color: var(--admin-blue) !important;
    box-shadow: 0 0 0 1px var(--admin-blue) !important;
}

.admin-page input[type="text"]::placeholder,
.admin-page input[type="email"]::placeholder,
.admin-page input[type="password"]::placeholder,
.admin-page textarea::placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
}

/* Button styling for admin pages */
.admin-page button {
    color: var(--light-text) !important;
}

.admin-page button:hover {
    background-color: var(--sidebar-accent) !important;
}

.admin-page button[data-variant="outline"] {
    background-color: transparent !important;
    border-color: var(--sidebar-border) !important;
    color: var(--light-text) !important;
}

.admin-page button[data-variant="outline"]:hover {
    background-color: var(--sidebar-accent) !important;
    border-color: var(--admin-blue) !important;
}

.admin-page button[data-variant="default"] {
    background-color: var(--admin-blue) !important;
    color: white !important;
}

.admin-page button[data-variant="default"]:hover {
    background-color: var(--admin-blue) !important;
    opacity: 0.9 !important;
}

/* Link styling for admin pages */
.admin-page a {
    color: var(--light-text) !important;
}

.admin-page a:hover {
    color: var(--admin-blue) !important;
}

@layer components {
  select option {
    background-color: white;
    color: black;
  }

  @media (prefers-color-scheme: dark) {
    select option {
      background-color: black;
      color: white;
    }
  }
}

/* Custom styles for Katalog page */
.font-main {
    font-family: var(--main-font);
}

/* Aside styles */
aside {
    width: 300px;
    background-color: var(--secondary-color);
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    height: 1150px;
}

.filter-section {
    width: 100%;
}

.filter-content-text {
    display: flex;
    align-items: flex-end;
    gap: 15px;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 15px;
}

.filter-content-text h1 {
    font-family: var(--main-font);
    font-size: var(--font-size-xlarge);
    font-weight: var(--font-weight-semibold);
    color: var(--grey-text);
    margin: 0;
    padding-bottom: 5px;
}

.filter-content-text img {
    width: 40px;
    height: 40px;
    margin-bottom: -5px;
}

.filter-content-text hr {
    display: block;
    width: 100%;
    height: 2px;
    background-color: #D9D9D9;
    border: none;
    margin: 0 0 0 0;
    position: absolute;
    bottom: 0;
    left: 0;
}

/* Katalog section styles */
.katalog-section {
    flex: 1;
    padding: 40px;
    display: flex;
    flex-direction: column;
    background-color: var(--secondary-color);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    width: 800px;
}

.katalog-content {
    flex: 1;
}

.katalog-header {
    margin-bottom: 10px;
}

.katalog-header h1 {
    font-family: var(--main-font);
    font-size: var(--font-size-xlarge);
    font-weight: var(--font-weight-bold);
    color: var(--grey-text);
    margin-bottom: 15px;
}

.katalog-header hr {
    border: none;
    border-top: 3px solid #D9D9D9;
    width: 250px;
    margin-bottom: 30px;
}

.katalog-products {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin: 10px 0 0 0;
}

.products {
    background-color: var(--secondary-color);
    text-align: left;
    border: #d9d9d9 solid 1px;
    width: 100%;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    position: relative;
    padding: 0;
}

.products a {
    text-decoration: none;
    color: inherit;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
}

.products img {
    width: 100%;
    height: 160px;
    object-fit: cover;
    margin: 0;
    display: block;
}

.products h1 {
    font-size: 14px;
    color: var(--grey-text);
    margin: 10px 0 15px 0;
    padding: 0 8px 0 8px;
    font-weight: var(--font-weight-semibold);
    width: 100%;
    text-align: left;
    display: flex;
    align-items: center;
    gap: 4px;
    line-height: 1.2;
}

.products p {
    color: var(--primary-color);
    font-size: 13px;
    font-weight: var(--font-weight-semibold);
    margin: 0;
    padding: 0 8px 10px 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-top: auto;
    line-height: 1.2;
}

.products .diskon-price {
    color: #fa766a;
    font-size: 14px;
    text-decoration: line-through;
    margin: 0;
    padding: 0 10px;
}

.products .price-cart {
    width: 30px;
    height: 25px;
}

.products .diskon-tag {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: #ff4444;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
}

.products .diskon-tag h2 {
    font-size: 12px;
    font-weight: bold;
    margin: 0;
}

.products .terbaru-tag {
    background-color: #22c55e;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: bold;
    margin-left: 4px;
}

/* Pagination styles */
.pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 30px;
}

.page-btn {
    padding: 8px 16px;
    border: 2px solid var(--primary-color);
    background: transparent;
    color: var(--primary-color);
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.page-btn.active,
.page-btn:hover {
    background: var(--primary-color);
    color: white;
}

/* Filter header styles */
.filter-header {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
}

.filter-header h2 {
    font-family: var(--main-font);
    font-size: var(--font-size-medium);
    font-weight: var(--font-weight-regular);
    margin: 0 20px 0 0;
    padding: 0 250px 0 0;
}

.filter-header img {
    height: 30px;
    width: 30px;
    margin: 0 0 0 10px;
}

/* Price filter dropdown specific styles */
.filter-price-dropdown {
    padding: 5px 15px;
    width: 250px;
    border: 2px solid #D9D9D9;
    background-color: var(--secondary-color);
    border-radius: 15px;
    position: absolute;
    z-index: 10;
    top: 395px;
}

.price-filter-options {
    padding: 0 0 10px 0;
}

.price-filter-options hr {
    width: 200px;
    margin-bottom: 15px;
}

.dropdown-button {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    width: 100%;
}

.dropdown-button span {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.dropdown-button span p {
    margin: 0;
}

.dropdown-button span img {
    margin-left: auto;
}

.dropdown-button-arrow {
    transition: transform 0.3s ease;
}

.price-filter-options .price-range {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-small);
}

/* Slider styles */
input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 4px;
    background: #D9D9D9;
    border-radius: 2px;
    outline: none;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

input[type="range"]::-webkit-slider-thumb:hover {
    transform: scale(1.1);
}

/* Custom checkbox styles */
.checkbox-item input[type="checkbox"]:checked::after {
    content: '';
    position: absolute;
    left: 4px;
    top: 0px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding: 0 5px;
}

.checkbox-item input[type="radio"] {
    appearance: none;
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid #D9D9D9;
    border-radius: 4px;
    margin-right: 10px;
    position: relative;
    cursor: pointer;
    vertical-align: middle;
    flex-shrink: 0;
}

.checkbox-item input[type="radio"]:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-item input[type="radio"]:checked::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 10px;
    height: 10px;
    background-color: white;
    border-radius: 2px;
}

.checkbox-item label {
    cursor: pointer;
    font-family: var(--main-font);
    font-size: var(--font-size-small);
    color: var(--grey-text);
    flex-grow: 1;
}

/* Product card styles */
.products {
    text-align: left;
}

.products .diskon-tag {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: #ff4444;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.products .terbaru-tag {
    background-color: #22c55e;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: bold;
    margin-left: 4px;
}

.products .diskon-price {
    color: #fa766a;
    text-decoration: line-through;
}

.products .price-cart {
    width: 30px;
    height: 25px;
}

/* Custom spacing utilities */
.w-75 {
    width: 300px;
}

.w-62\.5 {
    width: 250px;
}

.w-50 {
    width: 200px;
}

.h-12\.5 {
    height: 50px;
}

.w-12\.5 {
    width: 50px;
}

.gap-7\.5 {
    gap: 30px;
}

.gap-2\.5 {
    gap: 10px;
}

.mb-7\.5 {
    margin-bottom: 30px;
}

.mb-3\.75 {
    margin-bottom: 15px;
}

.mt-3\.75 {
    margin-top: 15px;
}

.pt-3\.75 {
    padding-top: 15px;
}

.pb-2\.5 {
    padding-bottom: 10px;
}

.px-3\.75 {
    padding-left: 15px;
    padding-right: 15px;
}

.py-1\.25 {
    padding-top: 5px;
    padding-bottom: 5px;
}

.px-1\.25 {
    padding-left: 5px;
    padding-right: 5px;
}

.pr-62\.5 {
    padding-right: 250px;
}

.rounded-3\.75 {
    border-radius: 15px;
}

.border-t-3 {
    border-top-width: 3px;
}

.w-4\.5 {
    width: 18px;
}

.h-4\.5 {
    height: 18px;
}

.mr-2\.5 {
    margin-right: 10px;
}

.w-7\.5 {
    width: 30px;
}

.h-6\.25 {
    height: 25px;
}

/* Additional admin background colors */
.admin-bg-gray {
    background-color: #6B7280 !important;
}

.admin-bg-gray:hover {
    background-color: #4B5563 !important;
}

/* Export dropdown styles */
.export-dropdown {
    @apply absolute right-0 top-full mt-1 w-48 bg-background border rounded-lg shadow-lg opacity-0 invisible transition-all duration-200 z-10;
}

.export-dropdown-trigger:hover .export-dropdown {
    @apply opacity-100 visible;
}
