<!-- <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login To The Site</title>
    <link rel="stylesheet" href="assets/css/login-page.css">
    <link rel="stylesheet" href="assets/css/output.css">
    <script defer src="./assets/js/login-page.js"></script>
</head>
<body>
    <main>
        <div class="cards">
            <div class="card-content">
                <div class="image-content">
                    <button type="button" onclick="switchLoginForm()">
                        Log In
                    </button>
                    <button type="button" onclick="switchSigninForm()">
                        Sign In
                    </button>
                </div>
                <div class="form-content">
                    <div class="heading-logo flex flex-row">
                        <div class="logo">
                            <img src="assets/images/tepian-teknologi-logo.jpg" draggable="false">
                        </div>
                        <div class="navigation">
                            <button type="button" onclick="window.location.href='main.html';">Return To Page</button>
                            <hr>
                            <button type="button" onclick="window.location.href=''">Need Help?</button>
                        </div>
                    </div>
                    <div id="login" class="login">
                        <form action="" method="POST" class="space-y-4">
                            <h4 class="text-xl text-center">Log In</h4>
                            <label for="username" class="text-sm font-medium text-black">Input Username :</label>
                            <div class="relative">
                                <input type="text" id="username" name="username" placeholder="Username" class="mt-1 block w-full px-3 py-2 border bg-blue-300 border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-700 focus:border-blue-700">
                                <button type="button" class="password-toggle absolute inset-y-0 right-0 px-3 flex items-center">
                                    <img src="assets/icons/user.svg" class="h-5 w-5 text-gray-500">
                                </button>
                            </div>
                            <label for="password" class="text-sm font-medium text-gray-700">Input Password :</label>
                            <div class="relative">
                                <input type="password" name="password" id="login-password" placeholder="Password" class="mt-1 block w-full px-3 py-2 border bg-blue-300 border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                                <button type="button" class="password-toggle absolute inset-y-0 right-0 px-3 flex items-center" data-target="login-password">
                                    <img src="assets/icons/eye.svg" class="h-5 w-5 text-gray-500">
                                </button>
                            </div>
                            <p class="text-end"><a href="">Forgot Password?</a></p>
                            <button type="submit" name="login" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-200 mt-4">
                                Submit
                            </button>
                        </form>
                        <br>
                        <p class="text-center">Don't Have an Account? <span><button onclick="switchSigninForm()">Create One!</button></span></p>
                    </div>
                    <div id="signin" class="signin hidden">
                        <form action="" method="POST" class="space-y-2">
                            <h4 class="text-xl text-center">Sign In</h4>
                            <label for="username" class="text-sm font-medium text-black">Input Username :</label>
                            <div class="relative">
                                <input type="text" id="username" name="username" placeholder="Username" class="mt-1 block w-full px-3 py-2 border bg-blue-300 border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-700 focus:border-blue-700">
                                <button type="button" class="password-toggle absolute inset-y-0 right-0 px-3 flex items-center">
                                    <img src="assets/icons/user.svg" class="h-5 w-5 text-gray-500">
                                </button>
                            </div>
                            <label for="phone" class="text-sm font-medium text-black">Input Your Whatsapp :</label>
                            <div class="relative">
                                <div class="flex">
                                    <span class="inline-flex items-center px-3 py-2 text-sm border border-r-0 bg-blue-300 border-gray-300 text-gray-500 rounded-l-md">
                                        +62
                                    </span>
                                    <input type="tel" id="phone" name="phone" placeholder="812-3456-7890" class="mt-0 block w-full px-3 py-2 border bg-blue-300 border-gray-300 rounded-r-md shadow-sm focus:outline-none focus:ring-blue-700 focus:border-blue-700">
                                </div>
                                <button type="button" class="password-toggle absolute inset-y-0 right-0 px-3 flex items-center">
                                    <img src="assets/icons/whatsapp.svg" class="h-5 w-5 text-gray-500">
                                </button>
                            </div>
                            <label for="password" class="text-sm font-medium text-gray-700">Input Password :</label>
                            <div class="relative">
                                <input type="password" name="password" id="password" placeholder="Password" class="mt-1 block w-full px-3 py-2 border bg-blue-300 border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                                <button type="button" class="password-toggle absolute inset-y-0 right-0 px-3 flex items-center" data-target="password">
                                    <img src="assets/icons/eye.svg" class="h-5 w-5 text-gray-500">
                                </button>
                            </div>
                            <button type="submit" name="signin" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-200 mt-4">
                                Create
                            </button>
                        </form>
                        <br>
                        <p class="text-center">Already Have One? <span><button onclick="switchLoginForm()">Use Existing!</button></span></p>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html> -->